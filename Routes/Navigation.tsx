import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Login from '../Screens/Login';
import Dashboard from '../Screens/QRscanner';
import Home from '../Screens/Home';
import Profile from '../Screens/Profile';

const Stack = createNativeStackNavigator();

const Navigation = (navigation: Object) => {
    return (
        <NavigationContainer>
            <Stack.Navigator
                initialRouteName='Login'
            >
                <Stack.Screen
                    name='Login'
                    component={Login}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name='Home'
                    component={Home}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name='Dashboard'
                    component={Dashboard}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name='Profile'
                    component={Profile}
                    options={{
                        headerShown: false
                    }}
                />

            </Stack.Navigator>
        </NavigationContainer>
    )
}

export default Navigation;