import React, {useEffect, useState} from 'react';
import {
  Alert,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import {httpPost} from './utils/http';
import Geolocation from 'react-native-geolocation-service';

import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ErrorHandlerPopup from '../Screens/component/ErrorHandlerPopup';
import OrderSuccessPopup from '../Screens/component/OrderSuccessPopup'

const Dashboard = (props: any) => {

  const {navigation} = props
  const [data, setData] = useState();
  const [datetoday, setDatetoday] = useState<Date>(new Date());
  const [granted, setGranted] = useState(false);
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>("")
  const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false)
  const [orderSuccessPopupVisibility, setOrderSuccessPopupVisibility] = useState<boolean>(false)
  const [orderSuccessPopupMessage, setOrderSuccessPopupMessage] = useState<string>('')


  const orderSuccessPopupClicked =(isVisible: boolean, message: string)=>{
    setOrderSuccessPopupVisibility(isVisible)
    setOrderSuccessPopupMessage(message)
  }

  const errorHandlerClicked = (errorHandlerVisibility: boolean, errorHandlerMessage: string) => {
    setErrorHandlerVisibility(errorHandlerVisibility)
    setErrorHandlerMessage(errorHandlerMessage)
  }

  useEffect(() => {
    grant();
  }, []);

  const grant = async () => {
    try {
      if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');
        if (status === 'granted') {
          setGranted(true);
        } else {
          setGranted(false);
        }
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setGranted(true);
        } else {
          setGranted(false);
        }
      }
    } catch (err) {
      setGranted(false);
    }
  };

  const dataConvert = (data: string) => {
    const pairs = data.split('\n').map(pair => {
      const [key, value] = pair.split(':');
      return [key.trim(), value.trim()];
    });
    const jsonObject = Object.fromEntries(pairs);
    GoToOrder(jsonObject);
  };

  const GoToOrder = async (jsonObject: any) => {
    if (granted) {
      Geolocation.getCurrentPosition(location => {
        AsyncStorage.getItem('DeviceToken').then((deviceToken)=>{
          const payload = {
            order_type: `${jsonObject?.OrderType}`,
            type: `${jsonObject?.Type}`,
            user_id: jsonObject?.ID,
            order_date: `${moment(datetoday).format('YYYY-MM-DD')}`,
            latitude: `${jsonObject?.Latitude}`,
            longitude: `${jsonObject?.Longitude}`,
            unique_key: `${jsonObject?.unique_key}`,
            file_name: `${jsonObject?.fileName}`,
            vendor_latitude: `${location?.coords?.latitude}`,
            vendor_longitude: `${location?.coords?.longitude}`,
            device_token: deviceToken
          };          
          httpPost('/api/scan_qr_code/', payload)
            .then(async (response: any) => {
              orderSuccessPopupClicked(true, JSON.parse(response)?.message)            
            })
            .catch(err => {
              if ( err?.message !== undefined &&  err?.message !== null &&  err?.message !== '') {
                errorHandlerClicked(true,  err.message)
              } else {
                errorHandlerClicked(true, 'Something went wrong. Please try again later.')
              }
  
            });
          return location;
        
        })
            
      }, err => {
        errorHandlerClicked(true, '')
      },
          { enableHighAccuracy: true, timeout: 15000 }
      );
  }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <QRCodeScanner
        onRead={({data}) => dataConvert(data)}
        reactivate={true}
        reactivateTimeout={3000}
        cameraType="back"
        topContent={
          <View>
            <Text style={styles.topText}>Scan to place order</Text>
          </View>
        }
        showMarker={true}
        markerStyle={styles.marker}
        topViewStyle={styles.topView}
        bottomViewStyle={styles.bottomView}
      />
      <ErrorHandlerPopup
      visible={errorHandlerVisibility}
      errorHandlerMessage={errorHandlerMessage}
      errorHandlerClicked={errorHandlerClicked}/>
      <OrderSuccessPopup
      navigation={navigation}
      visible={orderSuccessPopupVisibility}
      orderSuccessMessage={orderSuccessPopupMessage}
      orderSuccessPopupClicked={orderSuccessPopupClicked}/>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  topView: {
    marginBottom: 50,
  },
  topText: {
    fontWeight: '400',
    fontSize: 25,
    borderBottomWidth: 1,
    color: '#193C6D',
    borderBottomColor:'#193C6D',
    fontFamily: 'Poppins-Regular'
  },
  bottomView: {
    marginTop: 50,
  },
  bottomText: {
    color: '#193C6D',
  },
  marker: {
    borderWidth: 3,
    height: 300,
    width: 300,
    borderRadius: 30,
    borderColor: '#193C6D',
  },
});

export default Dashboard;
