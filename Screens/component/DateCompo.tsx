import React, { useEffect, useState } from 'react'
import { View, Image, StyleSheet, Text, Alert } from 'react-native';
import { httpGet } from "../utils/http";
import Spinner from "../component/Loader";

export const DateCompo = ()=>{
    const [data, setData] = useState();
    const [fetching, setFetching] = useState(false)
    
    useEffect(() => {
        setFetching(true)
        httpGet('/api/food_menu?from_date=2023-03-01&to_date=2023-03-27')
            .then((response: any) => {
                setData(JSON.parse(response).data)
                setFetching(false)
            }).catch(() => {
                Alert.alert("Something went wrong")
                setFetching(false)
            })
    }, [])
    

}