import React, {FC} from 'react';
import {Modal, View, Text} from "react-native";
import CardView from "react-native-cardview";
import Feather from "react-native-vector-icons/Feather";

type Props = {
    visible?: boolean,
    errorHandlerMessage: string,
    errorHandlerClicked: (errorHandlerVisibility: boolean, errorHandlerMessage: string ) => void
}

const ErrorHandlerPopup: FC<Props> = (props) => {
    const { visible, errorHandlerMessage, errorHandlerClicked } = props;

    if (visible) {
        setTimeout(function(){
            errorHandlerClicked(false, '')
        }, 3000);
     }

    return(
        <Modal transparent={visible}
               visible={visible}
               animationType="fade"
        >
            <View
                style={{
                    backgroundColor: 'rgba(0,0,0,0)',
                    flex: 1,
                    flexDirection: 'column',
                    justifyContent: 'flex-end',
                    paddingBottom: 25
                }}>
                <CardView
                    style={{
                        alignSelf: 'center',
                        backgroundColor: '#fff',
                        width: '90%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingTop: 9,
                        paddingBottom: 12,
                        paddingHorizontal: 9
                    }}
                    cardElevation={4}
                    cardMaxElevation={2}
                    cornerRadius={4}>
                    <Feather name="alert-circle" color={'#AF2C1B'} size={24} />
                    <Text style={{marginTop: 9, color: '#2F2F2F', fontWeight: '400', fontSize: 12, fontFamily: 'Poppins-Regular'}}>{errorHandlerMessage}</Text>
                </CardView>
            </View>
        </Modal>
    )
};

export default ErrorHandlerPopup;
