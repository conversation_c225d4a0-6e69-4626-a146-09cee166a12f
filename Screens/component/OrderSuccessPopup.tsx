import React, {FC} from 'react';
import {Modal, View, Text, TouchableOpacity} from "react-native";
import CardView from "react-native-cardview";

type Props = {
    visible?: boolean,
    orderSuccessMessage: string,
    orderSuccessPopupClicked: (isVisible: boolean, message: string ) => void
    navigation: any
}

const OrderSuccessPopup: FC<Props> = (props) => {
    const { visible, orderSuccessMessage, orderSuccessPopupClicked, navigation } = props;

    return(
        <Modal transparent={visible}
               visible={visible}
               animationType="fade"
        >
            <View
                style={{
                    backgroundColor: 'rgba(0,0,0,0)',
                    flex: 1,
                    flexDirection: 'column',
                    justifyContent: 'center'
                }}>
                <CardView
                    style={{
                        alignSelf: 'center',
                        backgroundColor: '#fff',
                        width: '90%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingTop: 9,
                        paddingBottom: 12,
                        paddingHorizontal: 9
                    }}
                    cardElevation={4}
                    cardMaxElevation={2}
                    cornerRadius={4}>
                    <Text style={{marginTop: 9, color: '#2F2F2F', fontWeight:'500', fontSize:16, fontFamily: 'Poppins-Regular'}}>{orderSuccessMessage}</Text>
                    <Text style={{marginTop: 9, color: '#2F2F2F', fontWeight:'400', fontSize:14, fontFamily: 'Poppins-Regular'}}>Do you want to go back?</Text>
                    <View style={{flexDirection:'row', marginTop:15}}>
                        <TouchableOpacity
                        onPress={()=>{orderSuccessPopupClicked(false,'')}}
                        style={{flex:1, backgroundColor:'#fff', justifyContent:'center', alignItems:'center', marginRight:10, borderRadius:4, paddingVertical:7, borderColor:'#193C6D', borderWidth:1}}>
                        <Text style={{ color: '#193C6D', fontWeight:'400', fontSize:13, fontFamily: 'Poppins-Regular'}}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity 
                        onPress={()=>{
                            orderSuccessPopupClicked(false, '')
                            navigation.goBack()
                        }}
                        style={{flex:1, backgroundColor:'#193C6D', justifyContent:'center', alignItems:'center', marginLeft:10, borderRadius:4, paddingVertical:7}}>
                        <Text style={{ color: '#fff', fontWeight:'400', fontSize:13, fontFamily: 'Poppins-Regular'}}>Confirm</Text>
                        </TouchableOpacity>
                    </View>
                </CardView>
            </View>
        </Modal>
    )
};

export default OrderSuccessPopup;
