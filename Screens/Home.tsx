import React, {useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Navigation from '../Routes/Navigation';

const Home = (props: any) => {
    const {navigation} = props


    const GoToScanner=()=>{
        navigation.navigate('Dashboard')
    }

    const GoToProfile=()=>{
        navigation.navigate('Profile')
    }



  return (
    <SafeAreaView style={styles.mainView}>
      <View>
        <TouchableOpacity onPress={()=>GoToProfile()}>
          <FontAwesome
            name="user-circle"
            color={'#193C6D'}
            size={50}
            style={styles.proImg}
          />
        </TouchableOpacity>
      </View>
      <View>
        <View>
          <Image
            source={require('../Images/logokuber.png')}
            style={styles.logoImg}
          />
        </View>
        <View>
          <Text style={styles.textHead}>Welcome!</Text>
        </View>
        <View>
          <TouchableOpacity style={styles.buttonView} onPress={()=>GoToScanner()}>
            <Text style={styles.textButton}>Tap to scan</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  proImg: {
    margin: 5
  },
  compoHome: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textHead: {
    color: 'black',
    height: 50,
    width: 250,
    textAlign: 'center',
    fontSize: 25,
    marginTop: 100,
    alignSelf: 'center',
    margin: 10,
    borderBottomColor: 'black',
    borderBottomWidth: 2,
    fontWeight: '500',
    fontFamily: 'Poppins-Regular'
  },
  logoImg: {
    height: 100,
    width: 230,
    alignSelf: 'center',
    top: 20,
    borderRadius: 5,
  },
  textButton: {
    color: 'white',
    alignContent: 'center',
    alignSelf: 'center',
    fontWeight: '400',
    fontSize: 14,
    fontFamily: 'Poppins-Regular'
  },
  buttonView: {
    display: 'flex',
    marginBottom: 10,
    borderWidth: 2,
    width: 200,
    height: 50,
    backgroundColor: '#193C6D',
    alignSelf: 'center',
    borderColor: 'white',
    borderRadius: 15,
    justifyContent: 'center',
  },
});

export default Home;
