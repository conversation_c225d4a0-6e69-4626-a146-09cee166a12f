import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { StackActions } from '@react-navigation/native';
import { httpPost } from '../Screens/utils/http';
import Spinner from '../Screens/component/Loader';
import validate from '../Screens/utils/validation';
import ErrorHandlerPopup from '../Screens/component/ErrorHandlerPopup';

const Login = (props: any) => {
  const { navigation } = props;
  const [userName, setUserName] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [passwordErrorMessage, setPasswordErrorMessage] = useState<String>('');
  const [loader, setLoader] = useState(false);
  const [emailError, setEmailError] = useState<string>("")
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>("")
  const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false)

  const stayLogin = async () => {
    setLoader(true);
    const usertoken = await AsyncStorage.getItem('AuthToken');
    if (usertoken !== null) {
      setLoader(false);
      navigation.dispatch(StackActions.replace('Home'));
    } else {
      setLoader(false);
    }
  };

  useEffect(() => {
    stayLogin();
  }, []);

  const errorHandlerClicked = (errorHandlerVisibility: boolean, errorHandlerMessage: string) => {
    setErrorHandlerVisibility(errorHandlerVisibility)
    setErrorHandlerMessage(errorHandlerMessage)
  }

  const loggedIn = () => {
    const userNameErrorMessage = validate("Email", userName);
    if (userNameErrorMessage) {
      setEmailError(userNameErrorMessage)

    } else if (userName.length < 1) {
      setEmailError("Please Enter Email")
    }
    else if (password.length < 1) {
      setPasswordErrorMessage("Please Enter Password")
    }

    else {
      setLoader(true);
      const payload = {
        username: userName,
        password: password,
      };
      httpPost('/api/vendor_login', payload)
        .then(async (response: any) => {
          const AuthToken = await JSON.parse(response)?.data?.token;
          const DeviceToken = await JSON.parse(response)?.data?.device_token;
          const FoodVendorName = await JSON.parse(response)?.data?.name;
          if (AuthToken) {
            AsyncStorage.setItem('DeviceToken', DeviceToken)
            AsyncStorage.setItem('AuthToken', AuthToken)
            AsyncStorage.setItem('FoodVendorName', FoodVendorName)
              .then(() => {
                setLoader(false);
                navigation.dispatch(StackActions.replace('Home'));
              })
              .catch(err => {
                setLoader(false);
              });
          }
        })
        .catch(err => {
          setLoader(false);
          if (err?.response?.data?.message !== undefined && err?.response?.data?.message !== null && err?.response?.data?.message !== '') {
            errorHandlerClicked(true, err?.response?.data?.message)
          } else {
            errorHandlerClicked(true, 'Something went wrong. Please try again later.')
          }
        });
    }

  };

  const getUserName = (text: string) => {
    setEmailError('')
    setPasswordErrorMessage('')
    setUserName(text);
  };

  const getPassWord = (passWord: string) => {
    setEmailError('')
    setPasswordErrorMessage('')
    setPassword(passWord);
  };

  return (
    <>
      <KeyboardAwareScrollView style={styles.mainView}>
        <SafeAreaView>
          <Image
            source={require('../Images/logokuber.png')}
            resizeMode='contain'
            style={styles.imageView}
          />
          <View style={styles.otherLogo}>
            <Text style={styles.textHead}>Email</Text>
            <TextInput
              style={[styles.emailId, emailError.length > 0 && {
                borderColor: '#AF2C1B'
              }]}
              returnKeyType="done"
              keyboardType="email-address"
              onChangeText={(text: string) => getUserName(text)}
            />{emailError &&
              <Text style={styles.textPass} >{emailError}</Text>
            }
            <View style={styles.MarginView}>
              <Text style={styles.textHead}>Password</Text>
              <TextInput
                style={[styles.passWord, passwordErrorMessage.length > 0 && {
                  borderColor: '#AF2C1B'
                }]}
                onChangeText={(passWord: string) => getPassWord(passWord)}
                autoCapitalize="none"
                autoCorrect={false}
                secureTextEntry={true}
                returnKeyType="go"
                placeholderTextColor={'black'}
              />
              <View>
                <View>
                  {passwordErrorMessage && (
                    <Text style={styles.textPass}>
                      {passwordErrorMessage}
                    </Text>
                  )}
                </View>
              </View>
            </View>
            <View style={styles.logButton}>
              <TouchableOpacity
                style={styles.Button}
                onPress={() => loggedIn()}>
                <Text style={styles.textButton}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </KeyboardAwareScrollView>
      <Spinner animating={loader} />
      <ErrorHandlerPopup
      visible={errorHandlerVisibility}
      errorHandlerMessage={errorHandlerMessage}
      errorHandlerClicked={errorHandlerClicked}/>
    </>
  );
};

const styles = StyleSheet.create({
  mainView: {
    backgroundColor: '#ececec',
    height: '100%',
    width: '100%',
    padding: 20
  },
  imageView: {
    height: 65,
    width: 166,
    alignSelf: 'center',
    marginTop: 90
  },
  emailId: {
    padding: 5,
    borderWidth: 1,
    borderRadius: 5,
    backgroundColor: '#ffffff',
    borderColor: '#193C6D',
    color: 'black',
  },
  passWord: {
    borderWidth: 1,
    padding: 5,
    borderRadius: 5,
    backgroundColor: '#ffffff',
    borderColor: '#193C6D',
    color: 'black',
  },
  forgotText: {
    color: '#C11828',
    margin: 2,
    alignSelf: 'flex-end',
  },
  Button: {
    borderWidth: 1,
    padding: 10,
    width: '100%',
    backgroundColor: '#193C6D',
    borderRadius: 5,
    borderColor: '#193C6D',
  },
  textButton: {
    textAlign: 'center',
    color: '#ffffff',
    fontWeight: '500',
    fontSize: 18,
    fontFamily: 'Poppins-Regular'
  },
  MarginView: {
    marginTop: 20
  },
  logButton: {
    marginTop: 95
    // margin: 15,
    // marginBottom: 153,
  },
  textHead: {
    marginBottom: 6,
    color: '#2F2F2F',
    fontWeight: '400',
    fontSize: 14,
    fontFamily: 'Poppins-Regular'
  },
  otherLogo: {
    marginTop: 80
  },
  textPass: {
    color: '#9D9D9D',
    paddingVertical: 5,
    fontStyle: 'italic',
    fontWeight: '400',
    fontSize: 12,
    fontFamily: 'Poppins-Regular'
  },
});

export default Login;
