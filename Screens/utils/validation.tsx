import validatejs from 'validate.js';
import {validation} from './validationConstraints';

export default function validate(fieldName: string, value: any) {
    var options = {
        fullMessages: false
    }
    const formValues: any = {};
    formValues[fieldName] = value;
    const formFields: any = {};
    if (value !== '' && value !== null) {
        formFields[fieldName] = validation() && validation()[fieldName];
        const result = validatejs(formValues, formFields, options);

        if (result) {
            return result[fieldName][0];
        }
    }
    return null;
}