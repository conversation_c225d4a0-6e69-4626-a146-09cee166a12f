import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const Profile = (props: any) => {
  const {navigation} = props;
  const [namedisplay, setNamedisplay] = useState<string>('');
  
  const removeData = async () => {
    try {
        await AsyncStorage.removeItem('AuthToken');
        navigation.reset({
            index: 0,
            routes: [{ name: 'Login' }]
        })
    } catch (error) {
        return false;
    }
}

  const profileFoodVendorName = async ()=>{
    const vendorName = await AsyncStorage.getItem('FoodVendorName');
    setNamedisplay(vendorName)
  }

  useEffect(()=>{
    profileFoodVendorName()
  },[])

  return (
    <SafeAreaView>
      <View>
        <FontAwesome
          name="user-circle"
          color={'#193C6D'}
          size={100}
          style={styles.proImg}
        />
      </View>
      <View>
        <Text style={styles.textName}>{namedisplay}</Text>
      </View>
      <View style={styles.viewLogout}>
        <MaterialCommunityIcons name="logout" size={20} color={'#193C6D'} />
        <TouchableOpacity style={{marginLeft: 10}} onPress={()=> removeData()}>
          <Text style={styles.textLogout}>Logout</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  proImg: {
    marginTop: 100,
    alignSelf: 'center',
  },
  textName: {
    color: 'black',
    fontSize: 20,
    alignSelf: 'center',
    margin:30,
    fontWeight: '400',
    fontFamily: 'Poppins-Regular'
  },
  viewLogout: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    margin: 20,
  },
  textLogout: {
    color: 'black',
    fontWeight: '400',
    fontSize: 14,
    fontFamily: 'Poppins-Regular'
  },
});

export default Profile;
