PODS:
  - boost (1.76.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.3)
  - FBReactNativeSpec (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.3)
    - RCTTypeSafety (= 0.71.3)
    - React-Core (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.1100)
  - Permission-Camera (3.3.0):
    - RNPermissions
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.71.3)
  - RCTTypeSafety (0.71.3):
    - FBLazyVector (= 0.71.3)
    - RCTRequired (= 0.71.3)
    - React-Core (= 0.71.3)
  - React (0.71.3):
    - React-Core (= 0.71.3)
    - React-Core/DevSupport (= 0.71.3)
    - React-Core/RCTWebSocket (= 0.71.3)
    - React-RCTActionSheet (= 0.71.3)
    - React-RCTAnimation (= 0.71.3)
    - React-RCTBlob (= 0.71.3)
    - React-RCTImage (= 0.71.3)
    - React-RCTLinking (= 0.71.3)
    - React-RCTNetwork (= 0.71.3)
    - React-RCTSettings (= 0.71.3)
    - React-RCTText (= 0.71.3)
    - React-RCTVibration (= 0.71.3)
  - React-callinvoker (0.71.3)
  - React-Codegen (0.71.3):
    - FBReactNativeSpec
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.3)
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/Default (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/DevSupport (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.3)
    - React-Core/RCTWebSocket (= 0.71.3)
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-jsinspector (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-Core/RCTWebSocket (0.71.3):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.3)
    - React-cxxreact (= 0.71.3)
    - React-jsc
    - React-jsi (= 0.71.3)
    - React-jsiexecutor (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - Yoga
  - React-CoreModules (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.3)
    - React-Codegen (= 0.71.3)
    - React-Core/CoreModulesHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-RCTBlob
    - React-RCTImage (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-cxxreact (0.71.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-jsinspector (= 0.71.3)
    - React-logger (= 0.71.3)
    - React-perflogger (= 0.71.3)
    - React-runtimeexecutor (= 0.71.3)
  - React-jsc (0.71.3):
    - React-jsc/Fabric (= 0.71.3)
    - React-jsi (= 0.71.3)
  - React-jsc/Fabric (0.71.3):
    - React-jsi (= 0.71.3)
  - React-jsi (0.71.3):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-perflogger (= 0.71.3)
  - React-jsinspector (0.71.3)
  - React-logger (0.71.3):
    - glog
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-safe-area-context (4.5.0):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - React-perflogger (0.71.3)
  - React-RCTActionSheet (0.71.3):
    - React-Core/RCTActionSheetHeaders (= 0.71.3)
  - React-RCTAnimation (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.3)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTAnimationHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTAppDelegate (0.71.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTBlobHeaders (= 0.71.3)
    - React-Core/RCTWebSocket (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-RCTNetwork (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTImage (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.3)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTImageHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-RCTNetwork (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTLinking (0.71.3):
    - React-Codegen (= 0.71.3)
    - React-Core/RCTLinkingHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTNetwork (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.3)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTNetworkHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTSettings (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.3)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTSettingsHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-RCTText (0.71.3):
    - React-Core/RCTTextHeaders (= 0.71.3)
  - React-RCTVibration (0.71.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.3)
    - React-Core/RCTVibrationHeaders (= 0.71.3)
    - React-jsi (= 0.71.3)
    - ReactCommon/turbomodule/core (= 0.71.3)
  - React-runtimeexecutor (0.71.3):
    - React-jsi (= 0.71.3)
  - ReactCommon/turbomodule/bridging (0.71.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.3)
    - React-Core (= 0.71.3)
    - React-cxxreact (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-logger (= 0.71.3)
    - React-perflogger (= 0.71.3)
  - ReactCommon/turbomodule/core (0.71.3):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.3)
    - React-Core (= 0.71.3)
    - React-cxxreact (= 0.71.3)
    - React-jsi (= 0.71.3)
    - React-logger (= 0.71.3)
    - React-perflogger (= 0.71.3)
  - RNCAsyncStorage (1.17.11):
    - React-Core
  - RNPermissions (3.3.0):
    - React-Core
  - RNScreens (3.20.0):
    - React-Core
    - React-RCTImage
  - RNVectorIcons (9.2.0):
    - React-Core
  - SocketRocket (0.6.0)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - OpenSSL-Universal (= 1.1.1100)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - libevent
    - OpenSSL-Universal
    - SocketRocket
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 60195509584153283780abdac5569feffb8f08cc
  FBReactNativeSpec: 9c191fb58d06dc05ab5559a5505fc32139e9e4a2
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  Permission-Camera: 597646618d1edcc055a3f660844c2ee6de8e0596
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: bec48f07daf7bcdc2655a0cde84e07d24d2a9e2a
  RCTTypeSafety: 171394eebacf71e1cfad79dbfae7ee8fc16ca80a
  React: d7433ccb6a8c36e4cbed59a73c0700fc83c3e98a
  React-callinvoker: 15f165009bd22ae829b2b600e50bcc98076ce4b8
  React-Codegen: df9d3975a10b29c1dfa4baa7678449e9d9648ca7
  React-Core: 882014e745cdad5788c5c8aa1a0f4661880aad69
  React-CoreModules: e0cbc1a4f4f3f60e23c476fef7ab37be363ea8c1
  React-cxxreact: bde3dde79b6d8e2dae76197ad75c11883f7d152d
  React-jsc: 30670396f92f5f9bab121bf8c303f5a0a507061b
  React-jsi: 103a8ff6586095e0e6853522f8eea37ca0226f99
  React-jsiexecutor: 891dad49e50d35fba97eeb43a35ae7470a8e10b2
  React-jsinspector: 9f7c9137605e72ca0343db4cea88006cb94856dd
  React-logger: 957e5dc96d9dbffc6e0f15e0ee4d2b42829ff207
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-safe-area-context: 39c2d8be3328df5d437ac1700f4f3a4f75716acc
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  React-perflogger: af8a3d31546077f42d729b949925cc4549f14def
  React-RCTActionSheet: 57cc5adfefbaaf0aae2cf7e10bccd746f2903673
  React-RCTAnimation: 11c61e94da700c4dc915cf134513764d87fc5e2b
  React-RCTAppDelegate: 3057d5a551fbe3ac8bac361761f4c74ba2e5ab17
  React-RCTBlob: 0e49a4ecd4d5276c79bea13d1c4b21f99da45121
  React-RCTImage: 7a9226b0944f1e76e8e01e35a9245c2477cdbabb
  React-RCTLinking: bbe8cc582046a9c04f79c235b73c93700263e8b4
  React-RCTNetwork: fc2ca322159dc54e06508d4f5c3e934da63dc013
  React-RCTSettings: f1e9db2cdf946426d3f2b210e4ff4ce0f0d842ef
  React-RCTText: 1c41dd57e5d742b1396b4eeb251851ce7ff0fca1
  React-RCTVibration: 5199a180d04873366a83855de55ac33ce60fe4d5
  React-runtimeexecutor: 7bf0dafc7b727d93c8cb94eb00a9d3753c446c3e
  ReactCommon: 5768a505f0bc7b798dc2becdd3ee6442224f796c
  RNCAsyncStorage: 8616bd5a58af409453ea4e1b246521bb76578d60
  RNPermissions: bcd846e8f5a7f39e921cc7ca7172e2de0e698b6f
  RNScreens: 218801c16a2782546d30bd2026bb625c0302d70f
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  SocketRocket: fccef3f9c5cedea1353a9ef6ada904fde10d6608
  Yoga: 5ed1699acbba8863755998a4245daa200ff3817b
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 18a99a98fc32e5ee7c66ff581cf757c454fa31f3

COCOAPODS: 1.11.3
