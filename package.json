{"name": "kuberscannerscanner", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "@react-navigation/native": "^6.0.14", "@react-navigation/native-stack": "^6.9.2", "axios": "^0.24.0", "moment": "^2.29.4", "react": "18.2.0", "react-native": "0.71.3", "react-native-camera": "^4.2.1", "react-native-cardview": "^2.0.5", "react-native-geolocation-service": "^5.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-permissions": "^3.3.0", "react-native-qrcode-scanner": "^1.5.5", "react-native-rename": "^3.2.12", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "^3.20.0", "react-native-splash-screen": "^3.3.0", "validate.js": "^0.13.1", "react-native-vector-icons": "^9.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.7", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}