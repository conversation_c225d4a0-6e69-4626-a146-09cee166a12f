import React, { useEffect } from 'react';
import {
  LogBox,
  SafeAreaView,
  StyleSheet
} from 'react-native';
import Navigation from './Routes/Navigation';
import SplashScreen from 'react-native-splash-screen';

function App() {
  LogBox.ignoreAllLogs();
  useEffect(() => {
    SplashScreen.hide();
  }, [])

  return (
    <SafeAreaView
      style={styles.mainView}
    >
      <Navigation />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  mainView: {
    flex: 1
  }
});

export default App;
